(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3185],{85644:function(n,e,o){Promise.resolve().then(o.bind(o,81523)),Promise.resolve().then(o.bind(o,70049)),Promise.resolve().then(o.t.bind(o,23735,23)),Promise.resolve().then(o.t.bind(o,77815,23)),Promise.resolve().then(o.t.bind(o,2778,23)),Promise.resolve().then(o.bind(o,18686)),Promise.resolve().then(o.bind(o,47245)),Promise.resolve().then(o.bind(o,3371)),Promise.resolve().then(o.bind(o,64528)),Promise.resolve().then(o.bind(o,11658)),Promise.resolve().then(o.bind(o,71917)),Promise.resolve().then(o.bind(o,19698))},47245:function(n,e,o){"use strict";o.d(e,{default:function(){return r}});var t=o(2265),i=o(64528);function r(n){let{launchingState:e}=n;return(0,t.useEffect)(()=>{i.Gd.getState().isLaunchingSoon!==e&&(console.log("Updating launching state from server:",e),i.Gd.setState({isLaunchingSoon:e}))},[e]),null}},19698:function(n,e,o){"use strict";o.r(e),o.d(e,{default:function(){return r}});var t=o(2265);let i=()=>{window.ankkor||(window.ankkor={}),window.ankkor.enableLaunchingSoon=()=>{console.warn("Changing launch state is disabled in production.")},window.ankkor.disableLaunchingSoon=()=>{console.warn("Changing launch state is disabled in production.")},window.ankkor.getLaunchingSoonStatus=()=>{let n=localStorage.getItem("ankkor-launch-state");if(!n)return!0;try{var e;let o=JSON.parse(n);return!!(null===(e=o.state)||void 0===e?void 0:e.isLaunchingSoon)}catch(n){return console.error("Failed to parse launch state",n),!0}}};var r=()=>((0,t.useEffect)(()=>{i()},[]),null)},2778:function(){}},function(n){n.O(0,[5540,4822,8787,8261,1539,2188,6003,7231,2323,9960,7696,8002,7111,8989,5717,8496,9429,8966,3903,6076,4596,62,6271,8726,8133,8049,2870,2461,632,6613,9965,5270,2647,9971,9290,6459,7800,4818,1729,5526,4615,7406,1225,2068,6518,4595,3280,8790,1104,7158,6758,7044,5302,6628,5363,4754,1744],function(){return n(n.s=85644)}),_N_E=n.O()}]);