(()=>{var e={};e.id=7928,e.ids=[7928],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},58188:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>l,routeModule:()=>g,tree:()=>o}),s(65413),s(41783),s(12523);var t=s(23191),a=s(88716),i=s(37922),n=s.n(i),c=s(95231),d={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>c[e]);s.d(r,d);let o=["",{children:["test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,65413)),"E:\\ankkorwoo\\ankkor\\src\\app\\test\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,41783)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],l=["E:\\ankkorwoo\\ankkor\\src\\app\\test\\page.tsx"],u="/test/page",m={require:s,loadChunk:()=>Promise.resolve()},g=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/test/page",pathname:"/test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},68302:(e,r,s)=>{Promise.resolve().then(s.bind(s,75072))},75290:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(76557).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},75072:(e,r,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.d(r,{default:()=>g});var a=s(10326),i=s(17577),n=s(91664),c=s(15725),d=s(54337),o=s(32913),l=s(75290),u=e([c,d,o]);[c,d,o]=u.then?(await u)():u;let m=({title:e,children:r})=>(0,a.jsxs)("div",{className:"mb-8 border rounded-md p-4",children:[a.jsx("h2",{className:"text-lg font-medium mb-4",children:e}),r]}),g=()=>{let[e,r]=(0,i.useState)([]),[s,t]=(0,i.useState)([]),[u,g]=(0,i.useState)(null),[h,x]=(0,i.useState)(null),[p,f]=(0,i.useState)({}),[v,b]=(0,i.useState)({}),j=(0,d.xS)(),y=(e,r)=>{f(s=>({...s,[e]:r}))},N=(e,r)=>{b(s=>({...s,[e]:r}))},w=async()=>{try{y("products",!0);let e=await (0,c.Xp)();r(e.nodes||[]),N("products",`Success! Fetched ${e.nodes?.length||0} products`)}catch(e){console.error("Error fetching products:",e),N("products",`Error: ${e.message}`)}finally{y("products",!1)}},k=async()=>{try{y("categories",!0);let e=await (0,c.CP)();t(e.nodes||[]),N("categories",`Success! Fetched ${e.nodes?.length||0} categories`)}catch(e){console.error("Error fetching categories:",e),N("categories",`Error: ${e.message}`)}finally{y("categories",!1)}},E=async()=>{if(!e.length){N("product","Error: No products available to test with");return}try{y("product",!0);let r=e[0].databaseId,s=await (0,c.wv)(r);g(s),N("product",`Success! Fetched product: ${s.name}`)}catch(e){console.error("Error fetching product:",e),N("product",`Error: ${e.message}`)}finally{y("product",!1)}},$=async()=>{if(!e.length){N("cart","Error: No products available to test with");return}try{y("cart",!0);let r=e[0];await j.addToCart({productId:r.databaseId.toString(),name:r.name,price:r.price,quantity:1,image:{url:r.image?.sourceUrl||"",altText:r.image?.altText||r.name}}),N("cart",`Success! Added ${r.name} to cart`)}catch(e){console.error("Error adding to cart:",e),N("cart",`Error: ${e.message}`)}finally{y("cart",!1)}},C=async()=>{try{y("login",!0);let e=await (0,o.x4)("<EMAIL>","password123");e&&(x(e),N("login",`Success! Logged in as ${e.email}`))}catch(e){console.error("Error logging in:",e),N("login",`Error: ${e.message}`)}finally{y("login",!1)}},P=async()=>{try{y("register",!0);let e=`test${Math.floor(1e4*Math.random())}@example.com`;await (0,o.z2)({email:e,firstName:"Test",lastName:"User",password:"password123",username:`testuser${Math.floor(1e4*Math.random())}`}),N("register",`Success! Registered user: ${e}`)}catch(e){console.error("Error registering:",e),N("register",`Error: ${e.message}`)}finally{y("register",!1)}},S=async()=>{try{y("currentUser",!0);let e=await (0,o.ts)();e?(x(e),N("currentUser",`Success! Current user: ${e.email}`)):N("currentUser","No user is currently logged in")}catch(e){console.error("Error getting current user:",e),N("currentUser",`Error: ${e.message}`)}finally{y("currentUser",!1)}};return(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[a.jsx("h1",{className:"text-2xl font-bold mb-6",children:"WooCommerce Integration Test"}),a.jsx(m,{title:"Products",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(n.z,{onClick:w,disabled:p.products,children:[p.products&&a.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Fetch Products"]}),v.products&&a.jsx("div",{className:`p-3 rounded-md ${v.products.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:v.products}),e.length>0&&(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("h3",{className:"font-medium mb-2",children:"First 5 Products:"}),a.jsx("ul",{className:"list-disc pl-5 space-y-1",children:e.slice(0,5).map(e=>(0,a.jsxs)("li",{children:[e.name," - $",e.price]},e.id))})]})]})}),a.jsx(m,{title:"Categories",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(n.z,{onClick:k,disabled:p.categories,children:[p.categories&&a.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Fetch Categories"]}),v.categories&&a.jsx("div",{className:`p-3 rounded-md ${v.categories.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:v.categories}),s.length>0&&(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("h3",{className:"font-medium mb-2",children:"Categories:"}),a.jsx("ul",{className:"list-disc pl-5 space-y-1",children:s.map(e=>a.jsx("li",{children:e.name},e.id))})]})]})}),a.jsx(m,{title:"Single Product",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(n.z,{onClick:E,disabled:p.product||!e.length,children:[p.product&&a.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Fetch Single Product"]}),v.product&&a.jsx("div",{className:`p-3 rounded-md ${v.product.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:v.product}),u&&(0,a.jsxs)("div",{className:"mt-4 p-4 border rounded-md",children:[a.jsx("h3",{className:"font-medium text-lg",children:u.name}),(0,a.jsxs)("p",{className:"text-gray-500 mt-1",children:["$",u.price]}),u.image&&a.jsx("div",{className:"mt-2 w-32 h-32 relative",children:a.jsx("img",{src:u.image.sourceUrl,alt:u.image.altText||u.name,className:"object-cover w-full h-full"})})]})]})}),a.jsx(m,{title:"Cart",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(n.z,{onClick:$,disabled:p.cart||!e.length,children:[p.cart&&a.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Add to Cart"]}),v.cart&&a.jsx("div",{className:`p-3 rounded-md ${v.cart.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:v.cart}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsxs)("h3",{className:"font-medium mb-2",children:["Cart Items: ",j.items.length]}),j.items.length>0&&a.jsx("ul",{className:"list-disc pl-5 space-y-1",children:j.items.map(e=>(0,a.jsxs)("li",{children:[e.name," - Qty: ",e.quantity]},e.id))})]})]})}),a.jsx(m,{title:"Authentication",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsxs)(n.z,{onClick:C,disabled:p.login,children:[p.login&&a.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Test Login"]}),(0,a.jsxs)(n.z,{onClick:P,disabled:p.register,variant:"outline",children:[p.register&&a.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Test Register"]}),(0,a.jsxs)(n.z,{onClick:S,disabled:p.currentUser,variant:"secondary",children:[p.currentUser&&a.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Get Current User"]})]}),v.login&&a.jsx("div",{className:`p-3 rounded-md ${v.login.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:v.login}),v.register&&a.jsx("div",{className:`p-3 rounded-md ${v.register.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:v.register}),v.currentUser&&a.jsx("div",{className:`p-3 rounded-md ${v.currentUser.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:v.currentUser}),h&&(0,a.jsxs)("div",{className:"mt-4 p-4 border rounded-md",children:[a.jsx("h3",{className:"font-medium",children:"Current User:"}),(0,a.jsxs)("p",{children:["Email: ",h.email]}),(0,a.jsxs)("p",{children:["Name: ",h.firstName," ",h.lastName]})]})]})})]})};t()}catch(e){t(e)}})},91664:(e,r,s)=>{"use strict";s.d(r,{z:()=>d});var t=s(10326);s(17577);var a=s(34214),i=s(79360),n=s(51223);let c=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:r,size:s,asChild:i=!1,...d}){let o=i?a.g7:"button";return t.jsx(o,{"data-slot":"button",className:(0,n.cn)(c({variant:r,size:s,className:e})),...d})}},51223:(e,r,s)=>{"use strict";s.d(r,{cn:()=>i});var t=s(41135),a=s(31009);function i(...e){return(0,a.m6)((0,t.W)(e))}},32913:(e,r,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.d(r,{ts:()=>a.ts,x4:()=>a.x4,z2:()=>a.z2});var a=s(61296),i=e([a]);a=(i.then?(await i)():i)[0],t()}catch(e){t(e)}})},65413:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>i});var t=s(19510);let a=(0,s(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\test\WooCommerceTest.tsx#default`);function i(){return t.jsx(a,{})}}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[8948,7499,2325,7204],()=>s(58188));module.exports=t})();