{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/status\\/redis(.json)?[\\/#\\?]?$", "originalSource": "/api/status/redis"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/account(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/account/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/checkout(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/checkout/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-in(.json)?[\\/#\\?]?$", "originalSource": "/sign-in"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-up(.json)?[\\/#\\?]?$", "originalSource": "/sign-up"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "a9yyOOZEbCCzpPgyYp3EL", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "sKjX6esX2tFN/yB6Jz4QtcsN8+YOPtdafJB9CkAwm9Y=", "__NEXT_PREVIEW_MODE_ID": "edde9837d234fa7127990bc65e6e6d66", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "35e45990e1995ce15fa7dbbe592847ac7be46fd32858426de2b01222b0cb729f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "771d6dfc8e5ee6bd09a67c4ad196e34048112d4a8d0ccd5dc40d268c19557c84"}}}, "functions": {}, "sortedMiddleware": ["/"]}