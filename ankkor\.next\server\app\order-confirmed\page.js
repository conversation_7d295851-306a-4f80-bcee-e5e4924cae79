(()=>{var e={};e.id=1978,e.ids=[1978],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},24541:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>l,routeModule:()=>u,tree:()=>o}),r(76469),r(41783),r(12523);var t=r(23191),a=r(88716),n=r(37922),i=r.n(n),d=r(95231),c={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>d[e]);r.d(s,c);let o=["",{children:["order-confirmed",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,76469)),"E:\\ankkorwoo\\ankkor\\src\\app\\order-confirmed\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,41783)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],l=["E:\\ankkorwoo\\ankkor\\src\\app\\order-confirmed\\page.tsx"],m="/order-confirmed/page",x={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/order-confirmed/page",pathname:"/order-confirmed",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},64305:(e,s,r)=>{Promise.resolve().then(r.bind(r,40927))},28916:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(76557).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},48705:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(76557).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},14228:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(76557).Z)("Truck",[["path",{d:"M5 18H3c-.6 0-1-.4-1-1V7c0-.6.4-1 1-1h10c.6 0 1 .4 1 1v11",key:"hs4xqm"}],["path",{d:"M14 9h4l4 4v4c0 .6-.4 1-1 1h-2",key:"11fp61"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}]])},40927:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>m});var t=r(10326),a=r(17577),n=r(35047),i=r(54659),d=r(28916),c=r(48705),o=r(14228),l=r(91664);function m(){let e=(0,n.useRouter)();(0,n.useSearchParams)();let[s,r]=(0,a.useState)(null);return s?t.jsx("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"max-w-2xl mx-auto text-center",children:[t.jsx("div",{className:"mb-8",children:t.jsx("div",{className:"mx-auto w-24 h-24 bg-green-100 rounded-full flex items-center justify-center",children:t.jsx(i.Z,{className:"w-12 h-12 text-green-600"})})}),(0,t.jsxs)("div",{className:"mb-8",children:[t.jsx("h1",{className:"text-3xl font-serif mb-4 text-gray-900",children:"Thank You for Your Order!"}),t.jsx("p",{className:"text-lg text-gray-600 mb-6",children:"Your order has been successfully placed and is being processed."}),(0,t.jsxs)("div",{className:"bg-gray-50 border rounded-lg p-6 mb-6",children:[t.jsx("h2",{className:"text-lg font-medium mb-2",children:"Order Details"}),(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[t.jsx("span",{className:"text-gray-600",children:"Order ID:"}),(0,t.jsxs)("span",{className:"font-mono text-lg font-medium text-[#2c2c27]",children:["#",s]})]})]})]}),(0,t.jsxs)("div",{className:"mb-8",children:[t.jsx("h3",{className:"text-xl font-medium mb-6",children:"What happens next?"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-3",children:t.jsx(d.Z,{className:"w-6 h-6 text-blue-600"})}),t.jsx("h4",{className:"font-medium mb-2",children:"Payment Confirmed"}),t.jsx("p",{className:"text-sm text-gray-600",children:"Your payment has been successfully processed"})]}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"mx-auto w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-3",children:t.jsx(c.Z,{className:"w-6 h-6 text-yellow-600"})}),t.jsx("h4",{className:"font-medium mb-2",children:"Order Processing"}),t.jsx("p",{className:"text-sm text-gray-600",children:"We're preparing your items for shipment"})]}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-3",children:t.jsx(o.Z,{className:"w-6 h-6 text-green-600"})}),t.jsx("h4",{className:"font-medium mb-2",children:"On the Way"}),t.jsx("p",{className:"text-sm text-gray-600",children:"Your order will be shipped soon"})]})]})]}),(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8",children:[t.jsx("h3",{className:"font-medium mb-2",children:"Order Confirmation Email"}),t.jsx("p",{className:"text-sm text-gray-600",children:"We've sent an order confirmation email with your order details and tracking information. Please check your inbox and spam folder."})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[t.jsx(l.z,{onClick:()=>e.push("/"),className:"w-full md:w-auto bg-[#2c2c27] hover:bg-[#3c3c37] text-white px-8 py-3",children:"Continue Shopping"}),t.jsx("div",{className:"text-center",children:t.jsx("button",{onClick:()=>e.push("/account"),className:"text-[#2c2c27] hover:underline text-sm",children:"View Order History"})})]}),(0,t.jsxs)("div",{className:"mt-12 pt-8 border-t border-gray-200",children:[t.jsx("h3",{className:"font-medium mb-4",children:"Need Help?"}),t.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"If you have any questions about your order, please don't hesitate to contact us."}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("p",{children:[t.jsx("span",{className:"font-medium",children:"Email:"})," ",t.jsx("a",{href:"mailto:<EMAIL>",className:"text-[#2c2c27] hover:underline",children:"<EMAIL>"})]}),(0,t.jsxs)("p",{children:[t.jsx("span",{className:"font-medium",children:"Phone:"})," ",t.jsx("a",{href:"tel:+**********",className:"text-[#2c2c27] hover:underline",children:"+91 12345 67890"})]})]})]})]})}):null}},91664:(e,s,r)=>{"use strict";r.d(s,{z:()=>c});var t=r(10326);r(17577);var a=r(34214),n=r(79360),i=r(51223);let d=(0,n.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c({className:e,variant:s,size:r,asChild:n=!1,...c}){let o=n?a.g7:"button";return t.jsx(o,{"data-slot":"button",className:(0,i.cn)(d({variant:s,size:r,className:e})),...c})}},51223:(e,s,r)=>{"use strict";r.d(s,{cn:()=>n});var t=r(41135),a=r(31009);function n(...e){return(0,a.m6)((0,t.W)(e))}},76469:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\order-confirmed\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[8948,7499,2325,7204],()=>r(24541));module.exports=t})();