(()=>{var e={};e.id=6757,e.ids=[6757],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},99842:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>l}),s(90782),s(41783),s(12523);var r=s(23191),a=s(88716),n=s(37922),o=s.n(n),i=s(95231),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(t,c);let l=["",{children:["test-auth",{children:["success",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,90782)),"E:\\ankkorwoo\\ankkor\\src\\app\\test-auth\\success\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,41783)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],d=["E:\\ankkorwoo\\ankkor\\src\\app\\test-auth\\success\\page.tsx"],u="/test-auth/success/page",p={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/test-auth/success/page",pathname:"/test-auth/success",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},73372:(e,t,s)=>{Promise.resolve().then(s.bind(s,48720))},90434:(e,t,s)=>{"use strict";s.d(t,{default:()=>a.a});var r=s(79404),a=s.n(r)},48720:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{default:()=>l});var a=s(10326),n=s(17577),o=s(90434),i=s(61296),c=e([i]);function l(){let[e,t]=(0,n.useState)(null),[s,r]=(0,n.useState)(!0);return a.jsx("div",{className:"container mx-auto py-12",children:(0,a.jsxs)("div",{className:"max-w-md mx-auto bg-white p-8 border border-gray-200",children:[a.jsx("h1",{className:"text-2xl font-bold mb-6 text-center",children:"Authentication Successful"}),s?a.jsx("p",{className:"text-center",children:"Loading user data..."}):e?(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("div",{className:"p-4 bg-green-50 border border-green-200 text-green-700",children:a.jsx("p",{className:"font-medium",children:"Successfully authenticated!"})}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("h2",{className:"text-lg font-medium",children:"User Information"}),(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"Name:"})," ",e.firstName," ",e.lastName]}),(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"Email:"})," ",e.email]}),(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"ID:"})," ",e.id]})]})]}):a.jsx("div",{className:"p-4 bg-yellow-50 border border-yellow-200 text-yellow-700",children:a.jsx("p",{children:"No user data found. Authentication may have failed."})}),a.jsx("div",{className:"mt-8 flex justify-center",children:a.jsx(o.default,{href:"/test-auth",className:"bg-[#2c2c27] text-white py-2 px-4 hover:bg-[#3c3c37]",children:"Back to Test Page"})})]})})}i=(c.then?(await c)():c)[0],r()}catch(e){r(e)}})},90782:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\test-auth\success\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,7499,9404,7204],()=>s(99842));module.exports=r})();