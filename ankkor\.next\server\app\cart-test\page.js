(()=>{var e={};e.id=1214,e.ids=[1214],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},5799:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>d}),r(36663),r(41783),r(12523);var s=r(23191),a=r(88716),o=r(37922),n=r.n(o),i=r(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d=["",{children:["cart-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,36663)),"E:\\ankkorwoo\\ankkor\\src\\app\\cart-test\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,41783)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],c=["E:\\ankkorwoo\\ankkor\\src\\app\\cart-test\\page.tsx"],p="/cart-test/page",u={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/cart-test/page",pathname:"/cart-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},47474:(e,t,r)=>{Promise.resolve().then(r.bind(r,93198))},93198:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(10326);r(17577);var a=r(86806);function o(){let e=(0,a.rY)();return(0,s.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[s.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Cart Test"}),(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx("button",{onClick:()=>{e.addToCart({productId:"123",quantity:1,name:"Test Product",price:"99.99",image:{url:"/placeholder-product.jpg",altText:"Test Product"}})},className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Add Test Item to Cart"}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h2",{className:"text-lg font-semibold",children:["Cart Items (",e.itemCount,")"]}),0===e.items.length?s.jsx("p",{children:"No items in cart"}):s.jsx("ul",{className:"space-y-2",children:e.items.map(t=>(0,s.jsxs)("li",{className:"border p-2 rounded",children:[(0,s.jsxs)("div",{children:[t.name," - ₹",t.price," x ",t.quantity]}),s.jsx("button",{onClick:()=>e.removeCartItem(t.id),className:"text-red-500 text-sm",children:"Remove"})]},t.id))})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{children:["Subtotal: ₹",e.subtotal().toFixed(2)]}),(0,s.jsxs)("p",{children:["Total: ₹",e.total().toFixed(2)]})]}),s.jsx("button",{onClick:e.clearCart,className:"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600",children:"Clear Cart"})]})]})}},36663:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\cart-test\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,7499,7204],()=>r(5799));module.exports=s})();